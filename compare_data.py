import csv

# --- 您需要修改的配置 ---

# “操作前”的CSV文件名
FILE_BEFORE = 'table_before.csv'
# “操作后”的CSV文件名
FILE_AFTER = 'table_after.csv'
# 表的主键列名 (非常重要！请确保这是能唯一标识一行的列名，通常是 'id')
PRIMARY_KEY_COLUMN = 'id'
# CSV文件的编码格式，Navicat 默认导出可能是 'utf-8' 或 'gbk'，如果报错请尝试修改
ENCODING = 'utf-8'

# -------------------------

def read_csv_to_dict(filename, key_column):
    """读取CSV文件并将其转换为以主键为key的字典"""
    data_dict = {}
    try:
        with open(filename, mode='r', encoding=ENCODING, newline='') as infile:
            reader = csv.DictReader(infile)
            for row in reader:
                # 检查主键列是否存在
                if key_column not in row:
                    print(f"错误: 在文件 '{filename}' 中找不到主键列 '{key_column}'。")
                    print(f"可用的列有: {', '.join(row.keys())}")
                    return None
                data_dict[row[key_column]] = row
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{filename}'。请确保文件名正确且文件在同一目录下。")
        return None
    except Exception as e:
        print(f"读取文件 '{filename}' 时发生错误: {e}")
        return None
    return data_dict

def compare_data():
    """主函数，用于对比两个CSV文件"""
    print("--- 开始对比 ---")

    data_before = read_csv_to_dict(FILE_BEFORE, PRIMARY_KEY_COLUMN)
    data_after = read_csv_to_dict(FILE_AFTER, PRIMARY_KEY_COLUMN)

    if data_before is None or data_after is None:
        print("由于读取文件出错，对比终止。")
        return

    # 将主键转换为集合，方便进行集合运算
    keys_before = set(data_before.keys())
    keys_after = set(data_after.keys())

    added_keys = keys_after - keys_before
    deleted_keys = keys_before - keys_after
    common_keys = keys_before & keys_after

    # 1. 查找新增的行
    if added_keys:
        print(f"\n---【新增了 {len(added_keys)} 行】---")
        for key in added_keys:
            print(f"  主键 '{PRIMARY_KEY_COLUMN}': {key}, 数据: {data_after[key]}")

    # 2. 查找删除的行
    if deleted_keys:
        print(f"\n---【删除了 {len(deleted_keys)} 行】---")
        for key in deleted_keys:
            print(f"  主键 '{PRIMARY_KEY_COLUMN}': {key}, 数据: {data_before[key]}")

    # 3. 查找修改的行
    modified_rows = []
    for key in common_keys:
        row_before = data_before[key]
        row_after = data_after[key]
        if row_before != row_after:
            changes = {}
            for field in row_before:
                if row_before[field] != row_after[field]:
                    changes[field] = {
                        '旧值': row_before[field],
                        '新值': row_after[field]
                    }
            modified_rows.append({'pk': key, 'changes': changes})

    if modified_rows:
        print(f"\n---【修改了 {len(modified_rows)} 行】---")
        for item in modified_rows:
            print(f"  主键 '{PRIMARY_KEY_COLUMN}': {item['pk']}")
            for field, values in item['changes'].items():
                print(f"    - 字段 '{field}': 从 '{values['旧值']}' -> 变为 '{values['新值']}'")

    print("\n--- 对比结束 ---")
    if not added_keys and not deleted_keys and not modified_rows:
        print("两个文件的数据完全一致，未发现任何增、删、改。")


if __name__ == '__main__':
    compare_data()